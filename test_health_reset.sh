#!/bin/bash

# 测试连接清理时重置目标健康状态的功能

echo "=== 测试连接清理时重置目标健康状态 ==="

# 停止现有进程
if pgrep -f rdp_forwarder > /dev/null; then
    echo "停止现有的 rdp_forwarder 进程..."
    pkill -f rdp_forwarder
    sleep 2
fi

# 创建测试配置
cat > test_health_config.conf << EOF
target_ip=**************
target_port=3389
listen_port=3389
listen_interface=0.0.0.0
max_clients=5
connection_timeout=60
verbose_logging=1
buffer_size=8192
enable_stats=1
stats_interval=15
transport_mode=tcp_only
enable_fast_reconnect=0
EOF

echo "启动 RDP Forwarder..."
./rdp_forwarder -c test_health_config.conf &
FORWARDER_PID=$!
echo "RDP Forwarder PID: $FORWARDER_PID"
sleep 3

echo ""
echo "=== 测试场景 1: 单连接断开后重置健康状态 ==="

# 模拟连接
echo "建立测试连接..."
if command -v nc > /dev/null; then
    # 创建一个短暂的连接
    timeout 3 nc localhost 3389 < /dev/null &
    NC_PID=$!
    sleep 1
    
    echo "强制断开连接..."
    kill -9 $NC_PID 2>/dev/null
    
    echo "等待连接清理和健康状态重置..."
    sleep 5
    
    echo "尝试新连接以验证健康状态已重置..."
    timeout 3 nc localhost 3389 < /dev/null &
    NC_PID2=$!
    sleep 1
    kill $NC_PID2 2>/dev/null
    
else
    echo "nc 命令不可用，跳过连接测试"
fi

echo ""
echo "=== 测试场景 2: 多连接情况下的健康状态管理 ==="

if command -v nc > /dev/null; then
    echo "建立多个测试连接..."
    
    # 建立第一个连接
    timeout 10 nc localhost 3389 < /dev/null &
    NC_PID1=$!
    sleep 1
    
    # 建立第二个连接
    timeout 10 nc localhost 3389 < /dev/null &
    NC_PID2=$!
    sleep 1
    
    echo "断开第一个连接..."
    kill -9 $NC_PID1 2>/dev/null
    sleep 2
    
    echo "断开第二个连接..."
    kill -9 $NC_PID2 2>/dev/null
    sleep 3
    
else
    echo "nc 命令不可用，跳过多连接测试"
fi

echo ""
echo "=== 检查日志输出 ==="
echo "查找健康状态重置相关的日志..."

# 检查系统日志中的相关条目
if command -v journalctl > /dev/null; then
    echo "系统日志中的相关条目："
    journalctl --since "2 minutes ago" | grep -E "(rdp_forwarder|Resetting target health|health status)" | tail -10
else
    echo "无法访问系统日志"
fi

echo ""
echo "=== 验证进程状态 ==="
if ps -p $FORWARDER_PID > /dev/null; then
    echo "RDP Forwarder 仍在运行"
    echo "进程信息："
    ps -p $FORWARDER_PID -o pid,ppid,cmd,etime
else
    echo "RDP Forwarder 进程已退出"
fi

echo ""
echo "=== 清理 ==="
echo "停止测试进程..."
kill $FORWARDER_PID 2>/dev/null
sleep 2

if ps -p $FORWARDER_PID > /dev/null; then
    echo "强制终止进程..."
    kill -9 $FORWARDER_PID 2>/dev/null
fi

rm -f test_health_config.conf

echo ""
echo "=== 测试完成 ==="
echo ""
echo "预期的日志输出应包含："
echo "1. 'Resetting target health status after connection cleanup' - 当没有其他活跃连接时"
echo "2. 'Target health status not reset - X other active connections remain' - 当有其他活跃连接时"
echo "3. 连接状态变化日志显示完整的生命周期"
echo ""
echo "这个改进的好处："
echo "- 避免因单个连接问题错误标记目标为不健康"
echo "- 智能判断是否需要重置（考虑其他活跃连接）"
echo "- 立即触发健康检查，加快恢复速度"
echo "- 提供详细的日志记录便于调试"
