# RDP Forwarder Configuration File
# 配置文件格式：key=value，支持注释（以#开头）

# 目标主机配置
target_ip=***************
target_port=3389

# 监听配置
listen_port=3389
listen_interface=0.0.0.0

# 连接管理
max_clients=10
connection_timeout=300
reconnect_interval=5

# 日志配置
verbose_logging=1
log_level=info
log_file=/var/log/rdp_forwarder.log

# 性能配置
buffer_size=8192
socket_timeout=30

# 监控配置
enable_stats=1
stats_interval=60

# 混合传输配置
transport_mode=hybrid
udp_preference=0.8
retransmit_timeout=100
max_retransmit=3
heartbeat_interval=1000

# 快速重连配置
enable_fast_reconnect=1
keep_target_alive=1
reconnect_delay=100
max_reconnect_attempts=5
connection_pool_size=2
